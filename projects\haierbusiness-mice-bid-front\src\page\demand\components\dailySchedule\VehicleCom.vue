<script setup lang="ts">
// 用车
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, toRaw, defineExpose } from 'vue';
import {
  CarUsageTypeConstant,
  UsageTimeTypeConstant,
  DemandSubmitObj,
  SeatTypeConstant,
  CarBrandTypeConstant,
} from '@haierbusiness-front/common-libs';
import { demandApi } from '@haierbusiness-front/apis';

const props = defineProps({
  dateIndex: {
    type: Number,
    default: 0,
  },
  vehicleList: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['demandPlanVehicleFunc', 'demandPlanRemoveFunc']);

const demandPlanFormRef = ref();

// 日程安排表单
const formState = reactive<DemandSubmitObj>({
  vehicles: [], // 用车
});

const routeIndex = ref<Number>(0);
const routeEditIndex = ref<Number>(0);
const routeEditIdx = ref<Number>(0);
const routeNameBegin = ref<String>('');
const routeName = ref<String>('');
const routeErr = ref<Boolean>(false);
const carBrandList = ref<Array>([]);

// 用车列表
watch(
  () => props.vehicleList,
  (newVal) => {
    formState.vehicles = [...newVal];
  },
  {
    immediate: true,
    deep: true,
  },
);

// 删除
const removeVehicle = (type: String, index: Number) => {
  emit('demandPlanRemoveFunc', { type: type, delIndex: index, index: props.dateIndex });
};

// 用车方式
const changeUsage = (index) => {
  formState.vehicles[index].routeList = [];
  formState.vehicles[index].route = '';
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandPlanFormRef.value
    .validate()
    .then(() => {
      emit('demandPlanVehicleFunc', { list: [...formState.vehicles], index: props.dateIndex });

      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;
    });

  return isVerifyPassed;
};
defineExpose({ onSubmit });

// 车型
const changeSeat = (index: number) => {
  carBrandList.value = CarBrandTypeConstant.toArray().filter((e) => e.seatNum === formState.vehicles[index].seats);

  calcPrice(index);
};

// 添加地点
const addCarRoute = (idx: Number) => {
  routeIndex.value = idx;
  routeName.value = '';
  routeErr.value = false;

  if (formState.vehicles[idx].routeList?.length === 0) {
    routeNameBegin.value = '';
  }
};
const routeConfirm = () => {
  return new Promise((resolve, reject) => {
    if (!routeNameBegin || !routeName.value) {
      routeErr.value = true;
      reject(false);
      return;
    }

    // 行程
    if (formState.vehicles[routeIndex.value].routeList?.length === 0) {
      formState.vehicles[routeIndex.value].routeList.push(routeNameBegin.value);
    }

    formState.vehicles[routeIndex.value].routeList.push(routeName.value);
    formState.vehicles[routeIndex.value].route = formState.vehicles[routeIndex.value].routeList.join(',');

    calcPrice(routeIndex.value);

    resolve(true);
  });
};

const routeConfirmEdit = () => {
  return new Promise((resolve, reject) => {
    if (!routeName.value) {
      routeErr.value = true;
      reject(false);
      return;
    }

    // 行程
    formState.vehicles[routeEditIndex.value].routeList[routeEditIdx.value] = routeName.value;
    formState.vehicles[routeEditIndex.value].route = formState.vehicles[routeEditIndex.value].routeList.join(',');

    calcPrice(routeEditIndex.value);

    resolve(true);
  });
};
// 编辑路线
const EditRoute = (vehiclesIndex: Number, i: Number) => {
  routeEditIndex.value = vehiclesIndex;
  routeEditIdx.value = i;
  routeName.value = formState.vehicles[vehiclesIndex].routeList[i] || '';
  routeErr.value = false;
};
// 删除路线
const delRoute = (vehiclesIndex: Number, i: Number) => {
  if (formState.vehicles[routeIndex.value].routeList.length === 2) {
    formState.vehicles[routeIndex.value].routeList = [];
    formState.vehicles[routeIndex.value].route = '';
  } else {
    formState.vehicles[routeIndex.value].routeList.splice(i, 1);
    formState.vehicles[routeIndex.value].route = formState.vehicles[routeIndex.value].routeList.join(',');
  }

  calcPrice(vehiclesIndex);
};

// 价格测算
const calcPrice = async (i: Number) => {
  if (
    (formState.vehicles[i].usageType == 0 || formState.vehicles[i].usageType == 1) &&
    formState.vehicles[i].seats &&
    formState.vehicles[i].seats > 0 &&
    formState.vehicles[i].vehicleNum &&
    formState.vehicles[i].vehicleNum > 0
  ) {
    let calcParams = {
      calcDate: formState.vehicles[i].demandDate + ' 00:00:00', // 需求日期
      usageType: formState.vehicles[i].usageType, // 使用方式

      seats: formState.vehicles[i].seats, // 座位数

      vehicleNum: formState.vehicles[i].vehicleNum, // 车辆数量
      brand: formState.vehicles[i].brand, // 品牌
      route: formState.vehicles[i].route, // 	路线,多程逗号分隔
    };

    if (formState.vehicles[i].usageTime == 0 || formState.vehicles[i].usageTime == 1) {
      calcParams.usageTime = formState.vehicles[i].usageTime; // 使用时长
    }

    const res = await demandApi.priceCalcVehicle({
      ...calcParams,
    });

    console.log('%c [ 用车-自动测算单价 ]-120', 'font-size:13px; background:pink; color:#bf2c9f;', res);

    formState.vehicles[i].calcUnitPrice = res.calcUnitPrice; //
  }
};

onMounted(async () => {});
</script>

<template>
  <!-- 用车 -->
  <div class="vehicle_com">
    <a-form ref="demandPlanFormRef" :model="formState" :labelCol="{ style: { width: '84px' } }" hideRequiredMark>
      <div class="plan_col_list mb20" v-for="(vehiclesItem, vehiclesIndex) in formState.vehicles" :key="vehiclesIndex">
        <div class="plan_col_title">
          {{ '用车' + (vehiclesIndex + 1) }}
        </div>
        <div class="plan_col_del" @click="removeVehicle('vehicle', vehiclesIndex)"></div>

        <a-row :gutter="12" class="mt20">
          <a-col :span="8">
            <a-form-item
              label="用车方式："
              :name="['vehicles', vehiclesIndex, 'usageType']"
              :rules="{
                required: true,
                message: '请选择用车方式',
                trigger: 'change',
              }"
            >
              <a-select
                v-model:value="vehiclesItem.usageType"
                @change="calcPrice(vehiclesIndex)"
                placeholder="请选择用车方式"
                allow-clear
              >
                <a-select-option
                  v-for="item in CarUsageTypeConstant.toArray()"
                  :key="item.code"
                  :value="item.code"
                  @click="changeUsage(vehiclesIndex)"
                >
                  {{ item.desc }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="车型："
              :name="['vehicles', vehiclesIndex, 'seats']"
              :rules="{
                required: true,
                message: '请选择车型',
                trigger: 'change',
              }"
            >
              <a-select
                v-model:value="vehiclesItem.seats"
                @change="changeSeat(vehiclesIndex)"
                placeholder="请选择车型"
                allow-clear
              >
                <a-select-option v-for="item in SeatTypeConstant.toArray()" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="型号："
              :name="['vehicles', vehiclesIndex, 'brand']"
              :rules="{
                required: false,
                message: '请选择型号',
                trigger: 'change',
              }"
            >
              <a-select v-model:value="vehiclesItem.brand" placeholder="请选择型号" allow-clear>
                <a-select-option v-for="item in carBrandList" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="用车数量："
              :name="['vehicles', vehiclesIndex, 'vehicleNum']"
              :rules="{
                required: true,
                message: '请填写用车数量',
                trigger: 'change',
              }"
            >
              <a-input-number
                v-model:value="vehiclesItem.vehicleNum"
                @blur="calcPrice(vehiclesIndex)"
                placeholder="请填写用车数量"
                allow-clear
                :min="1"
                :max="999999"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8" v-if="vehiclesItem.usageType == 1">
            <a-form-item
              label="使用时长："
              :name="['vehicles', vehiclesIndex, 'usageTime']"
              :rules="{
                required: true,
                message: '请选择使用时长',
                trigger: 'change',
              }"
              tooltip="方案提报时，以使用时长为准"
            >
              <a-select
                v-model:value="vehiclesItem.usageTime"
                @change="calcPrice(vehiclesIndex)"
                placeholder="请选择使用时长"
                allow-clear
              >
                <a-select-option v-for="item in UsageTimeTypeConstant.toArray()" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="24" v-if="vehiclesItem.usageType == 0">
            <div class="route_line">
              <a-form-item
                label="路线："
                :name="['vehicles', vehiclesIndex, 'route']"
                :rules="{
                  required: true,
                  message: '请添加路线',
                  trigger: 'change',
                }"
              >
                <a-input v-model:value="vehiclesItem.route" style="display: none" :maxlength="1000" />
              </a-form-item>

              <!-- 路线 -->
              <div class="route_content">
                <div class="route_list mb8" v-for="(item, index) in vehiclesItem.routeList" :key="index">
                  <div class="route_item">
                    <a-popconfirm
                      title="行程名称"
                      placement="topLeft"
                      ok-text="确定"
                      cancel-text="取消"
                      @confirm="routeConfirmEdit"
                    >
                      <template #description>
                        <div
                          class="pr16"
                          :style="{
                            width: '300px',
                          }"
                        >
                          <a-input
                            style="width: 290px"
                            v-model:value="routeName"
                            placeholder="请填写行程名称"
                            :maxlength="500"
                            allow-clear
                          />
                          <div v-show="routeErr" class="mt4" style="color: #ff4d4f">请填写行程名称</div>
                        </div>
                      </template>

                      <div class="route_flex" @click="EditRoute(vehiclesIndex, index)">
                        <span>{{ item }}</span>
                        <div class="route_edit ml2"></div>
                      </div>
                    </a-popconfirm>
                    <div class="route_del ml8" @click="delRoute(vehiclesIndex, index)"></div>
                  </div>
                  <div class="route_dash_line">
                    <div class="line"></div>
                  </div>
                </div>

                <a-popconfirm
                  title="行程名称"
                  placement="topLeft"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="routeConfirm"
                >
                  <template #description>
                    <div
                      class="pr16"
                      :style="{
                        width: vehiclesItem.routeList?.length === 0 ? '612px' : '300px',
                      }"
                    >
                      <a-input
                        v-show="vehiclesItem.routeList?.length === 0"
                        style="width: 290px"
                        v-model:value="routeNameBegin"
                        placeholder="请填写行程开始名称"
                        :maxlength="500"
                        allow-clear
                      />
                      <span v-show="vehiclesItem.routeList?.length === 0"> - </span>
                      <a-input
                        style="width: 290px"
                        v-model:value="routeName"
                        placeholder="请填写行程名称"
                        :maxlength="500"
                        allow-clear
                      />
                      <div v-show="routeErr" class="mt4" style="color: #ff4d4f">请填写行程名称</div>
                    </div>
                  </template>

                  <a-button class="add_route" @click="addCarRoute(vehiclesIndex)">
                    <template #icon>
                      <div class="add_img mr8"></div>
                    </template>
                    <span>添加地点</span>
                  </a-button>
                </a-popconfirm>
              </div>
            </div>
          </a-col>
          <a-col :span="24" v-else>
            <a-form-item
              label="路线概述："
              :name="['vehicles', vehiclesIndex, 'route']"
              :rules="{
                required: true,
                message: '请填写路线概述',
                trigger: 'change',
              }"
            >
              <a-textarea
                v-model:value="vehiclesItem.route"
                :autoSize="{ minRows: 3, maxRows: 3 }"
                allow-clear
                :maxlength="500"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </div>
</template>

<style scoped lang="less">
.vehicle_com {
  .plan_col_list {
    padding: 20px 24px;
    background: #fff;
    border-radius: 8px;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_vehicle.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }

    .plan_col_del {
      position: absolute;
      top: 12px;
      right: 12px;

      width: 18px;
      height: 18px;
      background: url('@/assets/image/demand/demand_del_x.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      cursor: pointer;
    }

    .route_line {
      display: flex;

      .route_content {
        display: flex;
        flex-wrap: wrap;

        .route_list {
          display: flex;
          padding-top: 2px;
          flex: 0 0 auto;

          .route_item {
            padding: 4px 12px 4px 16px;
            height: 28px;
            line-height: 28px;
            background: #f2f7fd;
            border-radius: 4px;

            color: #1868db;
            cursor: pointer;

            display: flex;
            justify-content: space-between;
            align-items: center;

            &:hover {
              .route_edit {
                width: 14px;
                height: 14px;
              }
            }

            .route_flex {
              display: flex;
              justify-content: space-between;
              align-items: center;
            }

            .route_edit {
              width: 14px;
              height: 14px;
              background: url('@/assets/image/demand/demand_edit.png');
              background-repeat: no-repeat;
              background-size: 100% 100%;
              cursor: pointer;
            }

            .route_del {
              width: 14px;
              height: 14px;
              background: url('@/assets/image/demand/demand_del_x_blue.png');
              background-repeat: no-repeat;
              background-size: 100% 100%;
              cursor: pointer;
            }
          }
        }

        .route_dash_line {
          position: relative;

          width: 28px;
          height: 28px;

          .line {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);

            width: 28px;
            height: 1px;
            border: 1px dashed #1868db;
          }
        }
      }
    }

    .add_route {
      padding: 0 20px;
      width: 116px;
      height: 28px;
      border: 1px solid rgba(24, 104, 219, 0.5);
      border-radius: 4px;
      display: flex;
      align-items: center;

      font-weight: 400;
      font-size: 14px;
      color: #1868db;

      .add_img {
        width: 12px;
        height: 12px;
        background: url('@/assets/image/demand/demand_add_blue.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}
</style>
