<script setup lang="ts">
// 会议预算
import { message } from 'ant-design-vue';
import { Item } from 'ant-design-vue/es/menu';
import { onMounted, ref, reactive, watch, defineProps, defineEmits } from 'vue';

const props = defineProps({
  validateNum: {
    // form表单校验触发
    type: Number,
    default: 1,
  },
  autoSaveNum: {
    // 暂存
    type: Number,
    default: 1,
  },
  isCalcNum: {
    type: Number,
    default: 1,
  },
  cacheStr: {
    type: String,
    default: '',
  },
  demandParams: {
    type: Object,
    default: {},
  },
});

const emit = defineEmits(['demandBudgetFunc']);

// watch(
//   () => props.validateNum,
//   (newVal) => {
//     if (newVal > 1) {
//       onSubmit();
//     }
//   },
// );
watch(
  () => props.autoSaveNum,
  (newVal) => {
    if (newVal > 1) {
      emit('demandBudgetFunc', {
        ...formState,
        isCalcVerify: true,
        isAutoSave: true,
      });
    }
  },
);
watch(
  () => props.isCalcNum,
  (newVal) => {
    if (newVal > 1) {
      // 会议预算
      let calcNum = 0;

      // 住宿
      props.demandParams.stays &&
        props.demandParams.stays.forEach((e) => {
          // 单价*房间数
          calcNum = calcNum + e.calcUnitPrice * e.roomNum;
        });
      console.log('%c [ 住宿 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      // 会场
      props.demandParams.places &&
        props.demandParams.places.forEach((e) => {
          calcNum = calcNum + e.calcUnitPlacePrice;

          if (e.calcUnitLedPrice) {
            // 单价*LED数量
            calcNum = calcNum + e.calcUnitLedPrice * e.ledNum;
          }
          if (e.calcUnitTeaPrice) {
            // 茶歇单价*会场人数
            calcNum = calcNum + e.calcUnitTeaPrice * e.personNum;
          }
        });
      console.log('%c [ 会场 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      // 用餐
      props.demandParams.caterings &&
        props.demandParams.caterings.forEach((e) => {
          // 单价*用餐人数
          calcNum = calcNum + e.calcUnitPrice * e.personNum;
        });
      console.log('%c [ 用餐 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      // 用车
      props.demandParams.vehicles &&
        props.demandParams.vehicles.forEach((e) => {
          // 价格*用车数量
          calcNum = calcNum + e.calcUnitPrice * e.vehicleNum;
        });
      console.log('%c [ 用车 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      // 服务人员
      props.demandParams.attendants &&
        props.demandParams.attendants.forEach((e) => {
          // 单价*人数
          calcNum = calcNum + e.calcUnitPrice * e.personNum;
        });
      console.log('%c [ 服务 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      // 拓展活动
      props.demandParams.activities &&
        props.demandParams.activities.forEach((e) => {
          // 单价*人数
          calcNum = calcNum + e.calcUnitPrice * e.personNum;
        });
      console.log('%c [ 活动 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      // 保险
      props.demandParams.insurances &&
        props.demandParams.insurances.forEach((e) => {
          // 单价*人数
          calcNum = calcNum + e.calcUnitPrice * e.personNum;
        });
      console.log('%c [ 保险 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      // 布展物料
      if (props.demandParams.material && props.demandParams.material.calcTotalPrice) {
        // 费用标准/总
        calcNum = calcNum + props.demandParams.material.demandTotalPrice;
      }
      console.log('%c [ 布展物料 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      // TODO
      // 票务预定
      if (props.demandParams.traffic && props.demandParams.traffic.calcTotalPrice) {
        calcNum = calcNum + props.demandParams.traffic.demandTotalPrice;
      }
      console.log('%c [ 票务 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      // 礼品需求
      props.demandParams.presents &&
        props.demandParams.presents.forEach((e) => {
          calcNum = calcNum + e.calcTotalPrice;
        });
      console.log('%c [ 礼品 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      // 其他需求
      props.demandParams.others &&
        props.demandParams.others.forEach((e) => {
          calcNum = calcNum + e.calcTotalPrice * e.num;
        });
      console.log('%c [ 其他需求 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      console.log('%c [ 测算金额 ]-64', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

      // 测算总金额
      formState.calcTotalPrice = calcNum;
      formState.demandTotalPrice = calcNum;

      emit('demandBudgetFunc', { ...formState, isCalcVerify: true, isAutoSave: false });
    }
  },
);

// 会议预算表单
const formState = reactive({
  calcTotalPrice: null, // 测算总金额 ,传入后和后端计算金额做校验
  demandTotalPrice: null, // 需求总金额
});

// 提交
const onSubmit = () => {
  // if (!formState.calcTotalPrice) {
  //   message.error('请预算金额');
  //   emit('demandBudgetFunc', { ...formState, isCalcVerify: false, isAutoSave: false });
  //   return;
  // }
  // emit('demandBudgetFunc', { ...formState, isCalcVerify: true, isAutoSave: false });
};

const height = ref('');

// 会务预算测算
const calculationBtn = () => {
  // TODO - 效果
  height.value = window.innerHeight + 'px';

  loaderShow();
  setTimeout(() => {
    loaderHide();
  }, 16000);
  emit('demandBudgetFunc', { ...formState, isCalcVerify: false, isAutoSave: false });
};

onMounted(async () => {
  // 反显
  if (props.cacheStr) {
    const cacheObj = JSON.parse(props.cacheStr);

    formState.calcTotalPrice = cacheObj.calcTotalPrice;
    formState.demandTotalPrice = cacheObj.demandTotalPrice;
  }
});

const loading = ref(false);

const loader = ref<any>();

const loaderShow = () => {
  loader.value = new window.SVGLoader(document.getElementById('loader'), {
    speedIn: 300,
    speedOut: 600,
    easingIn: mina.easeinout,
    easingOut: mina.bounce,
  });
  loader.value.show();
  setTimeout(() => {
    loading.value = true;
  }, 300);
};

const loaderHide = () => {
  loader.value.hide();
  loading.value = false;
};

const words = ref([
  {
    desc: '正在准备向AI服务发送查询指令',
    time: 2,
  },
  {
    desc: '系统正在初始化通信通道',
    time: 2,
  },
  {
    desc: '正在建立安全连接，保护数据隐私',
    time: 2,
  },
  {
    desc: 'AI正在理解您的会展需求',
    time: 2,
  },
  {
    desc: '正在对接行业数据库与历史案例库',
    time: 2,
  },
  {
    desc: '调用大数据模型进行价格预测与推荐',
    time: 2,
  },
  {
    desc: '正在综合多维度因素优化推荐方案',
    time: 2,
  },
  {
    desc: '数据已就绪！',
    time: 2,
  },
]);
</script>

<template>
  <!-- 会议预算 -->
  <div class="meeting_budget demand_pad24">
    <div class="budget_title">
      <span class="mr7">💰</span>
      <span>会议预算</span>
    </div>

    <div class="budget_contet mt12">
      <div class="budget_amount mr24">
        <div class="amount_label">预算金额</div>
        <div class="amount_num">
          <a-input-number
            v-model:value="formState.demandTotalPrice"
            placeholder=""
            allow-clear
            :min="1"
            :max="999999"
            :precision="2"
            class="amount_inp"
            :controls="false"
            style="width: 230px"
          />
          <span class="amount_unit ml6">元</span>
        </div>
      </div>

      <div class="budget_btn" @click="calculationBtn">
        <div class="calculation_btn_img mr10"></div>
        <span>会务预算测算</span>
      </div>

      <div v-show="formState.demandTotalPrice" class="budget_tip ml15">当前金额为测算金额，可修改</div>
    </div>
  </div>
  <div
    id="loader"
    :style="{ height: height }"
    class="pageload-overlay"
    data-opening="M 0,0 0,60 80,60 80,0 z M 80,0 40,30 0,60 40,30 z"
  >
    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 80 60" preserveAspectRatio="none">
      <path d="M 0,0 0,60 80,60 80,0 Z M 80,0 80,60 0,60 0,0 Z" />
    </svg>

    <div class="loading" v-if="loading">
      <div class="spinner">
        <div class="progressCon">
          <div class="title">AI正在对您的需求进行测算，请勿进行其他操作！</div>
          <div class="progress progress-striped progress-bar-animated">
            <div class="progress-bar progress-bar-striped"></div>
          </div>
          <div class="words">
            <div class="rw-words rw-words-1">
              <span v-for="(item, index) in words" :key="index" :style="{ '--i': index }">{{ item.desc }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.spinner {
  display: flex;
  width: 800px;
  height: 300px;
  flex-direction: column;
  align-items: center;

  .face {
    display: flex;
    width: 60px;
  }
}

.meeting_budget {
  background: linear-gradient(180deg, #d9e8ff 0%, #edf4ff 33%, #ffffff 100%);
  border-radius: 12px;
  border: 3px solid #ffffff;

  .budget_title {
    font-weight: 500;
    font-size: 20px;
    color: #1d2129;
  }

  .budget_contet {
    display: flex;
    align-items: center;

    .budget_amount {
      padding: 0 28px 0 24px;
      width: 378px;
      height: 56px;
      text-align: center;
      line-height: 56px;
      background: #d7e7ff;
      border-radius: 4px;

      display: flex;
      justify-content: space-between;

      .amount_label {
        color: #262626;
      }
      .amount_num {
        display: flex;
        align-items: center;

        .amount_inp {
        }
        :deep(.ant-input-number .ant-input-number-input) {
          text-align: right;
        }
        .amount_unit {
          font-weight: 500;
          color: #4e5969;
        }
      }
    }

    .budget_btn {
      width: 138px;
      height: 36px;
      background: linear-gradient(180deg, #35a1ef 0%, #1868db 100%);
      box-shadow: 0px 2px 8px 0px rgba(0, 103, 216, 0.1);
      border-radius: 4px;

      font-weight: 500;
      color: #ffffff;
      text-shadow: 0px 2px 8px rgba(0, 103, 216, 0.1);
      cursor: pointer;

      display: flex;
      justify-content: center;
      align-items: center;

      .calculation_btn_img {
        width: 14px;
        height: 20px;
        background: url('@/assets/image/demand/demand_calculation.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
    .budget_tip {
      color: #86909c;
      line-height: 22px;
    }
  }
}
</style>

<style scoped lang="less">
.title {
  display: flex;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  width: 100%;
  padding-bottom: 10px;
}

.loading {
  position: absolute;
  z-index: 10;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.container {
  display: none;
  min-height: 100%;
}

.container .show {
  display: block;
}

.pageload-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: none;
  z-index: 100;
}

.pageload-overlay.show {
  display: block;
}

.pageload-overlay svg {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.pageload-overlay svg path {
  fill: #fff;
}
</style>

<style scoped lang="less">
.progressCon {
  margin: 100px auto;
  width: 500px;
  text-align: center;
}

.progress {
  height: 30px;
  background: #fff;
  border-top: 5px solid #1c2647;
  border-bottom: 5px solid #1c2647;
  border-radius: 0;
  margin-bottom: 25px;
  overflow: visible;
  position: relative;
}
.progress:before,
.progress:after {
  content: '';
  width: 5px;
  background: #1c2647;
  position: absolute;
  top: 0;
  left: -5px;
  bottom: 0;
}
.progress:after {
  left: auto;
  right: -5px;
}

.progress-bar {
  height: 20px;
}

.progress .progress-bar {
  border: none;
  box-shadow: none;
  -webkit-animation: 2s linear none infinite running progress-bar-stripes, animate-positive 16s;
  animation: 2s linear none infinite running progress-bar-stripes, animate-positive 16s;
}

.progress-striped .progress-bar {
  background-color: #5bc0de;
  width: 100%;
}

@keyframes progress-bar-stripes {
  0% {
    background-position: 40px 0;
  }

  100% {
    background-position: 0 0;
  }
}

@keyframes animate-positive {
  0% {
    width: 0;
  }
}

.progress-bar-striped {
  background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-image: -o-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  -webkit-background-size: 40px 40px;
  background-size: 40px 40px;
}
</style>

<style scoped lang="less">
.words {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  height: 20px;
}

.rw-words {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
}
.rw-words-1 span {
  position: absolute;
  opacity: 0;
  overflow: hidden;
  color: #888;
  -webkit-transform-origin: 0% 0%;
  transform-origin: 0% 0%;
  -webkit-animation: rotateWord 16s linear 1 0s;
  -ms-animation: rotateWord 16s linear 1 0s;
  animation: rotateWord 16s linear infinite 0s;
}
.rw-words {
  span {
    -webkit-animation-delay: calc(2s * var(--i));
    -ms-animation-delay: calc(2s * var(--i));
    animation-delay: calc(2s * var(--i));
    color: #6b889d;
  }
}

@-webkit-keyframes rotateWord {
  0% {
    opacity: 0;
  }
  5% {
    opacity: 0;
  }
  17% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
  }
  19% {
    opacity: 1;
    -webkit-transform: rotate(98deg);
  }
  21% {
    opacity: 1;
    -webkit-transform: rotate(86deg);
  }
  23% {
    opacity: 1;
    -webkit-transform: translateY(85px) rotate(83deg);
  }
  25% {
    opacity: 0;
    -webkit-transform: translateY(170px) rotate(80deg);
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}
@-ms-keyframes rotateWord {
  0% {
    opacity: 0;
  }
  5% {
    opacity: 0;
  }
  17% {
    opacity: 1;
    -ms-transform: rotate(0deg);
  }
  19% {
    opacity: 1;
    -ms-transform: rotate(98deg);
  }
  21% {
    opacity: 1;
    -ms-transform: rotate(86deg);
  }
  23% {
    opacity: 1;
    -ms-transform: translateY(85px) rotate(83deg);
  }
  25% {
    opacity: 0;
    -ms-transform: translateY(170px) rotate(80deg);
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}
@keyframes rotateWord {
  0% {
    opacity: 0;
  }
  5% {
    opacity: 0;
  }
  17% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  19% {
    opacity: 1;
    -webkit-transform: rotate(98deg);
    transform: rotate(98deg);
  }
  21% {
    opacity: 1;
    -webkit-transform: rotate(86deg);
    transform: rotate(86deg);
  }
  23% {
    opacity: 1;
    -webkit-transform: translateY(85px) rotate(83deg);
    transform: translateY(85px) rotate(83deg);
  }
  25% {
    opacity: 0;
    -webkit-transform: translateY(170px) rotate(80deg);
    transform: translateY(170px) rotate(80deg);
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}
</style>
