<script setup lang="ts">
// 会场
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, toRaw, defineExpose } from 'vue';
import {
  hotelLevelAllConstant,
  PlaceUsageTimeTypeConstant,
  UsagePurposeTypeConstant,
  TableTypeConstant,
  DemandSubmitObj,
  StaysArr,
  DemandCalcPlaceObj,
} from '@haierbusiness-front/common-libs';
import { demandApi } from '@haierbusiness-front/apis';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  dateIndex: {
    type: Number,
    default: 0,
  },
  meetingPersonTotal: {
    // 会议人数
    type: Number,
    default: 0,
  },
  placeList: {
    type: Array,
    default: [],
  },
  hotelList: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['demandPlanPlaceFunc', 'demandPlanRemoveFunc']);

const demandPlanFormRef = ref();
const tableTypeVisible = ref<boolean>(false);

import U from '@/assets/image/tableType/U.png';
import DIRECTOR from '@/assets/image/tableType/DIRECTOR.png';
import THEATER from '@/assets/image/tableType/THEATER.png';
import ISLAND from '@/assets/image/tableType/ISLAND.png';
import DRINK from '@/assets/image/tableType/DRINK.png';
import DESK from '@/assets/image/tableType/DESK.png';
import FISH from '@/assets/image/tableType/FISH.png';
import CIRCLE from '@/assets/image/tableType/CIRCLE.png';
// 摆台形式
const tableImgList = reactive<array>([
  { name: '海岛式', url: ISLAND },
  { name: 'U型式', url: U },
  { name: '董事会式', url: DIRECTOR },
  { name: '剧院式', url: THEATER },
  { name: '酒会式', url: DRINK },
  { name: '课桌式', url: DESK },
  { name: '鱼骨式', url: FISH },
  { name: '回形式', url: CIRCLE },
]);

// 日程安排表单
const formState = reactive<DemandSubmitObj>({
  placesList: [], // 会场
});

const hotelList = ref<Array>([]); // 酒店列表
const hotelChangeObj = ref<DemandCalcPlaceObj>({}); // 酒店选择

// 酒店选择列表
watch(
  () => props.hotelList,
  (newList) => {
    hotelList.value = newList.filter((e) => e.centerMarker);

    setTimeout(() => {
      // 会场
      formState.placesList.forEach((e) => {
        // 酒店需求只有一个时，默认选中
        if (hotelList.value?.length === 1) {
          e.tempDemandHotelId = hotelList.value[0].tempDemandHotelId;
          e.level = hotelList.value[0].level;

          hotelChangeObj.value = hotelList.value[0];
        }

        if (e.tempDemandHotelId) {
          const checkedHotel = hotelList.value.filter((j) => j.tempDemandHotelId === e.tempDemandHotelId);

          if (checkedHotel.length === 0) {
            e.tempDemandHotelId = null;
            e.level = null;
            hotelChangeObj.value = {};
          }
        }
      });
    }, 0);
  },
  {
    immediate: true,
    deep: true,
  },
);

// 会场列表
watch(
  () => props.placeList,
  (newVal) => {
    formState.placesList = [...newVal];
  },
  {
    immediate: true,
    deep: true,
  },
);

// 删除
const removePlace = (type: String, index: Number) => {
  emit('demandPlanRemoveFunc', { type: type, delIndex: index, index: props.dateIndex });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandPlanFormRef.value
    .validate()
    .then(() => {
      emit('demandPlanPlaceFunc', { list: [...formState.placesList], index: props.dateIndex });

      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;
    });

  return isVerifyPassed;
};
defineExpose({ onSubmit });

// 酒店选择
const changeHotel = (obj: StaysArr, i: Number) => {
  // 星级-赋值
  const hotelObj = hotelList.value.filter((e) => e.tempDemandHotelId == obj.tempDemandHotelId);
  formState.placesList[i].level = hotelObj[0]?.level || null;

  hotelChangeObj.value = hotelObj[0] || {};
  calcPrice(i);
};

// 移除LED
const removeLed = (index: number) => {
  formState.placesList[index].hasLed = false;
  formState.placesList[index].ledNum = null;
  formState.placesList[index].ledSpecs = null;
};
// 移除tea
const removeTea = (index: number) => {
  formState.placesList[index].hasTea = false;
  formState.placesList[index].teaEachTotalPrice = null;
  formState.placesList[index].teaDesc = null;
};

// 价格测算
const calcPrice = async (i: Number) => {
  if (
    formState.placesList[i].tempDemandHotelId &&
    formState.placesList[i].usageTime &&
    formState.placesList[i].usagePurpose &&
    formState.placesList[i].personNum &&
    formState.placesList[i].tableType
  ) {
    if (hotelChangeObj.value && Object.keys(hotelChangeObj.value).length === 0) {
      hotelChangeObj.value = hotelList.value[0];
    }

    const calcParams = {
      calcDate: formState.placesList[i].demandDate + ' 00:00:00', // 需求日期
      usageTime: formState.placesList[i].usageTime, // 使用时间
      usagePurpose: formState.placesList[i].usagePurpose, // 使用用途

      personNum: formState.placesList[i].personNum, // 人数
      area: formState.placesList[i].area, // 面积
      underLightFloor: formState.placesList[i].underLightFloor, // 灯下层高
      tableType: formState.placesList[i].tableType, // 摆台形式
      hasLed: formState.placesList[i].hasLed, // 是否需要led
      ledNum: formState.placesList[i].ledNum, // 	led数量
      ledSpecs: formState.placesList[i].ledSpecs === '以服务商提报为准' ? null : formState.placesList[i].ledSpecs, // 	led规格说明
      hasTea: formState.placesList[i].hasTea, // 	是否需要茶歇
      teaEachTotalPrice: formState.placesList[i].teaEachTotalPrice, // 	茶歇标准/每人
      teaDesc: formState.placesList[i].teaDesc, // 	茶歇说明

      cityId: hotelChangeObj.value?.cityId || null, // 酒店所在城市id
      districtIds: hotelChangeObj.value?.districtIds || '', // 酒店所在区域id,支持多区域,逗号分割\n 如果多个区域,则多个区域同时测算,并返回最高的区域的价格
      latitude: hotelChangeObj.value?.latitude || '', // 需求中心点经度
      longitude: hotelChangeObj.value?.longitude || '', // 需求中心点纬度
      distanceRange: hotelChangeObj.value?.distanceRange || null, // 需求范围:单位米(可选)
      centerMarker: hotelChangeObj.value?.centerMarker || '', //	需求中心的地标名称
    };

    const res = await demandApi.priceCalcPlace({
      ...calcParams,
    });

    console.log('%c [ 会场-自动测算单价 ]-120', 'font-size:13px; background:pink; color:#bf2c9f;', res);

    formState.placesList[i].calcUnitPlacePrice = res.calcUnitPlacePrice; // 自动测算会场单价
    formState.placesList[i].calcUnitLedPrice = res.calcUnitLedPrice; // 自动测算led单价
    formState.placesList[i].calcUnitTeaPrice = res.calcUnitTeaPrice; // 自动测算茶歇单价
  }
};

onMounted(async () => {});
</script>

<template>
  <!-- 会场 -->
  <div class="place_com">
    <a-form ref="demandPlanFormRef" :model="formState" :labelCol="{ style: { width: '84px' } }" hideRequiredMark>
      <div class="plan_col_list mb20" v-for="(placesItem, placesIndex) in formState.placesList" :key="placesIndex">
        <div class="plan_col_title">
          {{ '会场' + (placesIndex + 1) }}
        </div>
        <div class="plan_col_del" @click="removePlace('place', placesIndex)"></div>

        <a-row :gutter="12" class="mt20">
          <a-col :span="8">
            <a-row>
              <a-col :span="placesItem.level ? 17 : 24">
                <a-form-item
                  label="酒店选择："
                  :name="['placesList', placesIndex, 'tempDemandHotelId']"
                  :rules="{
                    required: true,
                    message: '请选择酒店',
                    trigger: 'change',
                  }"
                  tooltip="酒店选择来自酒店需求"
                >
                  <a-select
                    v-model:value="placesItem.tempDemandHotelId"
                    @change="changeHotel(placesItem, placesIndex)"
                    :disabled="hotelList && hotelList.length === 1"
                    placeholder="请选择酒店"
                    allow-clear
                  >
                    <a-select-option
                      v-for="(item, idx) in hotelList"
                      :key="item.tempDemandHotelId"
                      :value="item.tempDemandHotelId"
                    >
                      <a-tooltip placement="topLeft" :title="'酒店' + (idx + 1) + '-' + item.centerMarker">
                        {{ '酒店' + (idx + 1) + '-' + item.centerMarker }}
                      </a-tooltip>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-show="placesItem.level" :span="7">
                <a-form-item label="">
                  <a-select v-model:value="placesItem.level" placeholder="请选择酒店星级" disabled>
                    <a-select-option
                      v-for="item in hotelLevelAllConstant.toArray()"
                      :key="item.code"
                      :value="item.code"
                    >
                      {{ item.desc }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="使用时间："
              :name="['placesList', placesIndex, 'usageTime']"
              :rules="{
                required: true,
                message: '请选择使用时间',
                trigger: 'change',
              }"
            >
              <a-select
                v-model:value="placesItem.usageTime"
                @change="calcPrice(placesIndex)"
                placeholder="请选择使用时间"
                allow-clear
              >
                <a-select-option
                  v-for="item in PlaceUsageTimeTypeConstant.toArray()"
                  :key="item.code"
                  :value="item.code"
                >
                  {{ item.desc }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="会场用途："
              :name="['placesList', placesIndex, 'usagePurpose']"
              :rules="{
                required: true,
                message: '请选择会场用途',
                trigger: 'change',
              }"
            >
              <a-select
                v-model:value="placesItem.usagePurpose"
                @change="calcPrice(placesIndex)"
                placeholder="请选择会场用途"
                allow-clear
              >
                <a-select-option v-for="item in UsagePurposeTypeConstant.toArray()" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="参会人数："
              :name="['placesList', placesIndex, 'personNum']"
              :rules="{
                required: true,
                message: '请填写参会人数',
                trigger: 'change',
              }"
            >
              <a-input-number
                v-model:value="placesItem.personNum"
                @blur="calcPrice(placesIndex)"
                placeholder="请填写参会人数"
                allow-clear
                :min="1"
                :max="props.meetingPersonTotal || 99999"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="摆台形式："
              :name="['placesList', placesIndex, 'tableType']"
              :rules="{
                required: true,
                message: '请选择摆台形式',
                trigger: 'change',
              }"
            >
              <a-select
                v-model:value="placesItem.tableType"
                @change="calcPrice(placesIndex)"
                placeholder="请选择摆台形式"
                allow-clear
              >
                <a-select-option v-for="item in TableTypeConstant.toArray()" :key="item.code" :value="item.code">
                  {{ item.desc }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <QuestionCircleOutlined class="table_type_tip" @click="tableTypeVisible = true" />
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="会场面积："
              :name="['placesList', placesIndex, 'area']"
              :rules="{
                required: false,
                message: '请填写会场面积',
                trigger: 'change',
              }"
            >
              <a-input-number
                v-model:value="placesItem.area"
                placeholder="非必填，默认以供应商提报为准"
                addon-after="㎡"
                allow-clear
                :min="1"
                :max="999999"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="灯下层高："
              :name="['placesList', placesIndex, 'underLightFloor']"
              :rules="{
                required: false,
                message: '请填写灯下层高',
                trigger: 'change',
              }"
            >
              <a-input-number
                v-model:value="placesItem.underLightFloor"
                placeholder="非必填，默认以供应商提报为准"
                addon-after="m"
                allow-clear
                :min="1"
                :max="999999"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-button v-if="!placesItem.hasLed" class="plan_add_btn" @click="placesItem.hasLed = true">
              <template #icon>
                <div class="plan_add_img mr5"></div>
              </template>
              <span>添加LED</span>
            </a-button>

            <a-row v-else class="place_led pt24">
              <a-col :span="8">
                <a-form-item
                  label="LED数量："
                  :labelCol="{ style: { width: '115px' } }"
                  :name="['placesList', placesIndex, 'ledNum']"
                  :rules="{
                    required: true,
                    message: '请填写LED数量',
                    trigger: 'change',
                  }"
                >
                  <a-input-number
                    v-model:value="placesItem.ledNum"
                    @blur="calcPrice(placesIndex)"
                    placeholder="请填写LED数量"
                    allow-clear
                    :min="1"
                    :max="999999"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="15">
                <a-form-item
                  label="规格说明："
                  :name="['placesList', placesIndex, 'ledSpecs']"
                  :rules="{
                    required: false,
                    message: '请填写规格说明',
                    trigger: 'change',
                  }"
                >
                  <a-input
                    v-model:value="placesItem.ledSpecs"
                    placeholder="请填写规格说明"
                    :maxlength="500"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="1">
                <div class="place_del" @click="removeLed(placesIndex)"></div>
              </a-col>
            </a-row>
          </a-col>
          <a-col :span="24" class="mt24">
            <a-button v-if="!placesItem.hasTea" class="plan_add_btn" @click="placesItem.hasTea = true">
              <template #icon>
                <div class="plan_add_img mr5"></div>
              </template>
              <span>添加茶歇</span>
            </a-button>

            <a-row v-else class="place_led pt24">
              <a-col :span="8">
                <a-form-item
                  label="茶歇费用标准："
                  :labelCol="{ style: { width: '115px' } }"
                  :name="['placesList', placesIndex, 'teaEachTotalPrice']"
                  :rules="{
                    required: true,
                    message: '请填写茶歇费用标准',
                    trigger: 'change',
                  }"
                >
                  <a-input-number
                    v-model:value="placesItem.teaEachTotalPrice"
                    @blur="calcPrice(placesIndex)"
                    placeholder="请填写茶歇费用标准"
                    addon-after="元/位"
                    allow-clear
                    :min="1"
                    :max="999999"
                    :precision="2"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="15">
                <a-form-item
                  label="茶歇说明："
                  :name="['placesList', placesIndex, 'teaDesc']"
                  :rules="{
                    required: false,
                    message: '请填写茶歇说明',
                    trigger: 'change',
                  }"
                >
                  <a-input
                    v-model:value="placesItem.teaDesc"
                    placeholder="请填写茶歇说明"
                    :maxlength="500"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="1">
                <div class="place_del" @click="removeTea(placesIndex)"></div>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
      </div>
    </a-form>
    <!-- 摆台形式 -->
    <a-modal v-model:open="tableTypeVisible" title="摆台形式" width="800px">
      <div v-for="(item, index) in tableImgList" :key="index" class="demand_table_type">
        <div class="table_type_title">{{ item.name }}</div>
        <a-image :src="item.url" :preview="false" />
      </div>
      <template #footer></template>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.place_com {
  .plan_col_list {
    padding: 20px 24px;
    background: #fff;
    border-radius: 8px;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_place.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }

    .plan_col_del {
      position: absolute;
      top: 12px;
      right: 12px;

      width: 18px;
      height: 18px;
      background: url('@/assets/image/demand/demand_del_x.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      cursor: pointer;
    }
  }

  .table_type_tip {
    margin-top: 6px;
    font-size: 18px;
    cursor: pointer;
  }

  .plan_add_btn {
    padding: 0 20px;
    width: 108px;
    height: 24px;
    border: 2px solid rgba(24, 104, 219, 0.2);
    border-radius: 2px;
    display: flex;
    align-items: center;

    font-weight: 400;
    font-size: 12px;
    color: #1868db;

    .plan_add_img {
      width: 12px;
      height: 12px;
      background: url('@/assets/image/demand/demand_add_blue.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  .place_del {
    margin: 8px auto;
    width: 16px;
    height: 16px;
    background: url('@/assets/image/demand/demand_del_heng.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    cursor: pointer;
  }

  .place_led {
    background: #f7f8fa;
    border-radius: 8px;
    border: 1px solid #e5e6eb;
  }
}
</style>
<style>
.demand_table_type {
  .table_type_title {
    margin-top: 20px;
    padding: 5px 10px;
    font-size: 18px;
    text-align: center;
  }
  &:first-child {
    .table_type_title {
      margin-top: 0px;
    }
  }
}
</style>
