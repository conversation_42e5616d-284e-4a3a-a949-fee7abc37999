<script setup lang="ts">
// 其他需求
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, defineExpose } from 'vue';
import { DemandSubmitObj, OthersArr } from '@haierbusiness-front/common-libs';
import { demandApi } from '@haierbusiness-front/apis';

const props = defineProps({
  cacheStr: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['demandOtherFunc']);

const demandOthersFormRef = ref();

// 其他需求表单
const formState = reactive<DemandSubmitObj>({
  // 其他需求
  others: [],
});
// 其他需求
const otherParams = ref<OthersArr>({
  // 需求其他需求明细
  // demandDate: null, // 需求日期
  num: null, // 数量
  itemName: null, // 项目
  unit: null, // 单位
  specs: '以服务商提报为准', // 规格描述
  demandTotalPrice: null, // 总预算

  calcTotalPrice: null, // 自动测算总价
});

// 锚点 - 用户点击添加需求，需跳转至添加位置
const anchorId = (id: string) => {
  document?.getElementById(id)?.scrollIntoView({
    behavior: 'smooth', //smooth:平滑，auto：直接定位
    block: 'center',
    inline: 'start',
  });
};

// 提交
const onSubmit = async () => {
  let isVerifyPassed = false;

  await demandOthersFormRef.value
    .validate()
    .then(() => {
      emit('demandOtherFunc', { ...formState });

      isVerifyPassed = true;
    })
    .catch((err) => {
      isVerifyPassed = false;

      anchorId('demandOthersId');
    });

  return isVerifyPassed;
};

// 暂存
const tempSave = () => {
  emit('demandOtherFunc', { ...formState });
};

defineExpose({ onSubmit, tempSave });

// 其他需求 - 删除
const removeGood = (index: Number) => {
  formState.others.splice(index, 1);
};
// 添加
const goodAdd = () => {
  formState.others.push(JSON.parse(JSON.stringify(otherParams.value)));
};

// 获取当前时间
function getCurrentDateTime() {
  const now = new Date();
  const year = now.getFullYear();
  let month = now.getMonth() + 1;
  let day = now.getDate();
  let hour = now.getHours();
  let minute = now.getMinutes();
  let second = now.getSeconds();

  // 格式化月、日、时、分、秒为两位数
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  hour = hour < 10 ? '0' + hour : hour;
  minute = minute < 10 ? '0' + minute : minute;
  second = second < 10 ? '0' + second : second;

  let dateTime = '' + year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;

  return dateTime;
}

// 价格测算
const calcPrice = async (i: Number) => {
  if (formState.others[i].num && formState.others[i].demandTotalPrice) {
    const calcParams = {
      calcDate: getCurrentDateTime(), //测算日期
      num: formState.others[i].num, //
      demandTotalPrice: formState.others[i].demandTotalPrice, // 总预算

      itemName: formState.others[i].itemName, //
      unit: formState.others[i].unit, //
      specs: formState.others[i].specs === '以服务商提报为准' ? null : formState.others[i].specs, //
    };

    const res = await demandApi.priceCalcOther({
      ...calcParams,
    });

    console.log('%c [ 其他-自动测算单价 ]-120', 'font-size:13px; background:pink; color:#bf2c9f;', res);

    formState.others[i].calcTotalPrice = res.calcTotalPrice; //

    emit('demandOtherFunc', { ...formState });
  }
};

onMounted(async () => {
  goodAdd();

  // 反显
  if (props.cacheStr) {
    const cacheObj = JSON.parse(props.cacheStr);
    if (cacheObj.others && Object.keys(cacheObj.others).length > 0) {
      formState.others = [...cacheObj.others];
    }
  }
});
</script>

<template>
  <!-- 其他需求 -->
  <div class="demand_other demand_pad24">
    <div class="demand_title">
      <div class="demand_border"></div>
      <span>其他需求</span>
    </div>

    <a-form
      class="mt20"
      ref="demandOthersFormRef"
      :model="formState"
      :labelCol="{ style: { width: '84px' } }"
      hideRequiredMark
    >
      <div class="plan_col_list mb20" v-for="(goodItem, goodIndex) in formState.others" :key="goodIndex">
        <div class="plan_col_title">
          {{ '其他' + (goodIndex + 1) }}
        </div>
        <div v-show="goodIndex > 0" class="plan_col_del" @click="removeGood(goodIndex)"></div>

        <a-row :gutter="12" class="mt20" id="demandOthersId">
          <a-col :span="8">
            <a-form-item
              label="项目："
              :name="['others', goodIndex, 'itemName']"
              :rules="{
                required: true,
                message: '请填写项目',
                trigger: 'change',
              }"
            >
              <a-input v-model:value="goodItem.itemName" placeholder="请填写项目" :maxlength="200" allow-clear />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="单位："
              :name="['others', goodIndex, 'unit']"
              :rules="{
                required: true,
                message: '请填写单位',
                trigger: 'change',
              }"
            >
              <a-input v-model:value="goodItem.unit" placeholder="请填写单位" :maxlength="20" allow-clear />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="使用数量："
              :name="['others', goodIndex, 'num']"
              :rules="{
                required: true,
                message: '请填写使用数量',
                trigger: 'change',
              }"
            >
              <a-input-number
                v-model:value="goodItem.num"
                @blur="calcPrice(goodIndex)"
                placeholder="请填写使用数量"
                allow-clear
                :min="1"
                :max="999999"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              label="总预算："
              :name="['others', goodIndex, 'demandTotalPrice']"
              :rules="{
                required: true,
                message: '请填写总预算',
                trigger: 'change',
              }"
            >
              <a-input-number
                v-model:value="goodItem.demandTotalPrice"
                @blur="calcPrice(goodIndex)"
                placeholder="请填写总预算"
                addon-after="元"
                allow-clear
                :min="1"
                :max="999999"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="16">
            <a-form-item
              label="规格描述："
              :name="['others', goodIndex, 'specs']"
              :rules="{
                required: true,
                message: '请填写规格描述',
                trigger: 'change',
              }"
            >
              <a-input
                v-model:value="goodItem.specs"
                @blur="calcPrice(goodIndex)"
                placeholder="请填写规格描述"
                :maxlength="500"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>

    <div class="plan_btns">
      <a-button class="plan_add_btn" type="primary" @click="goodAdd">
        <template #icon>
          <div class="plan_add_img mr8"></div>
        </template>
        <span>添加新需求</span>
      </a-button>
    </div>
  </div>
</template>

<style scoped lang="less">
.demand_other {
  .plan_col_list {
    padding: 20px 24px 0;
    background: #f6f9fc;
    border-radius: 8px;
    border: 1px solid #e5e6e8;

    position: relative;

    .plan_col_title {
      background: url('@/assets/image/demand/demand_other.png');
      background-repeat: no-repeat;
      background-size: 18px 18px;
      background-position: left center;

      text-indent: 28px;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: #1d2129;
    }

    .plan_col_del {
      position: absolute;
      top: 12px;
      right: 12px;

      width: 18px;
      height: 18px;
      background: url('@/assets/image/demand/demand_del_x.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      cursor: pointer;
    }
  }

  .plan_btns {
    user-select: none;

    .plan_add_btn {
      padding: 0 15px;
      width: 132px;
      height: 36px;
      box-shadow: 0px 2px 8px 0px rgba(0, 103, 216, 0.1);
      border-radius: 4px;
      display: flex;
      align-items: center;

      font-weight: 500;
      font-size: 14px;
      color: #fff;

      .plan_add_img {
        width: 20px;
        height: 20px;
        background: url('@/assets/image/demand/demand_add_white.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}
</style>
