<script setup lang="ts">
import { defineProps, ref, reactive, computed } from 'vue';
import { Modal } from 'ant-design-vue';
import {
  SearchParams,
  MiceBidManOrderList,
  OrderListResponse,
  OrderListConstant,
  PlatformCount,
  ProcessNode,
  SelTabObj,
  TabList,
  CountSum,
} from '@haierbusiness-front/common-libs';
import LikeImg from '@/assets/image/like.png';
import PersonImg from '@/assets/image/person.png';
import { miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { useRouter } from 'vue-router';
interface Props {
  data: {
    name: string;
    desc: string;
    avatar: string;
    link?: string;
    isRecommend: boolean;
    performance: { label: string; value: string }[];
    statistics: { label: string; value: string }[];
  };
}
const searchParams = reactive<SearchParams>({
  // processNodes: 'DEMAND_PRE_INTERACT, DEMAND_PUSH, SCHEME_APPROVAL, BID_PUSH, BILL_APPROVAL',
  states: [],
  keyword: undefined,
  demandItem: undefined,
  mainCode: undefined,
  miceName: undefined,
  miceType: undefined,
  operatorCode: undefined,
  operatorName: undefined,
  operatorPhone: undefined,
  consultantUserCode: undefined,
  consultantUserName: undefined,
  contactUserCode: undefined,
  contactUserName: undefined,
  contactUserPhone: undefined,
  districtType: undefined,
  pageNum: 1,
  pageSize: 10,
});
const { data } = defineProps<Props>();
const emit = defineEmits(['assign']);
const router = useRouter();
const open = ref(false);
const columns = ref([
  {
    title: '会议名称',
    dataIndex: 'miceName',
    key: 'miceName',
    // width: 280,
    ellipsis: true,
  },
  {
    title: '节点',
    dataIndex: 'processNode',
    key: 'processNode',
    width: 200,
    ellipsis: true,
  },
  {
    title: '参会人数',
    dataIndex: 'personTotal',
    key: 'personTotal',
    width: 100,
    ellipsis: true,
  },
  {
    title: '经办人',
    dataIndex: 'operatorName',
    key: 'operatorName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '会议日期',
    dataIndex: 'startDate',
    key: 'startDate',
    // width: 230,
    ellipsis: true,
  },
]);
const orderList = ref<MiceBidManOrderList[]>([]);
const total = ref(0);
let temp = '';
const handleLink = async (link: string) => {
  searchParams.consultantUserCode = link;
  ProcessNode.toArray().forEach((item) => {
    if (item?.code !== 'END') temp += item.code + ',';
  });
  searchParams.processNodes = temp;
  const res = (await miceBidManOrderListApi.orderList(searchParams)) as OrderListResponse;
  orderList.value = JSON.parse(JSON.stringify(res.records));
  total.value = res.total;
  // window.open(link);
  open.value = true;
};
const pagination = computed(() => ({
  total: total.value,
  current: searchParams.pageNum,
  pageSize: searchParams.pageSize,
}));
const handleChange = async (page) => {
  searchParams.pageNum = page.current;
  searchParams.pageSize = page.pageSize;
  const res = (await miceBidManOrderListApi.orderList(searchParams)) as OrderListResponse;
  orderList.value = JSON.parse(JSON.stringify(res.records));
  total.value = res.total;
};
const handleAssign = (data: Props['data']) => {
  Modal.confirm({
    title: '确认指派',
    icon: null,
    okText: '确定',
    cancelText: '取消',
    content: `确认要指派给 ${data.name} 吗？`,
    onOk() {
      emit('assign');
    },
  });
};
</script>

<template>
  <!-- 顾问卡片组件 -->
  <div class="consultant-card">
    <div class="consultant-card-left">
      <img :src="data.path" />
      <a @click="handleLink(data.link)">查看会议明细</a>
    </div>
    <div class="consultant-card-right">
      <div class="consultant-card-right-top">
        <span class="consultant-card-name">{{ data.name }}</span>
        <span class="consultant-card-desc">{{ data.desc }}</span>
      </div>
      <div class="consultant-card-right-middle">
        <div
          class="consultant-card-performance"
          v-for="(performanceItem, index) of data.performance"
          :key="index"
          :style="{ marginLeft: index % 2 === 1 ? '20px' : 0 }"
        >
          <div
            class="consultant-card-performance-icon"
            :style="{ background: index % 2 === 1 ? '#FAAD14' : '#1868DB' }"
          />
          <span class="consultant-card-performance-label">{{ performanceItem.label }}</span>
          <span class="consultant-card-performance-divider">|</span>
          <span class="consultant-card-performance-value" :style="{ color: index % 2 === 1 ? '#FAAD14' : '#1868DB' }">{{
            performanceItem.value
          }}</span>
        </div>
      </div>
      <div class="consultant-card-right-bottom">
        <div class="consultant-card-statistics">
          <div class="consultant-card-statistic" v-for="statisticItem of data.statistics" :key="statisticItem.value">
            <div class="consultant-card-statistic-value">{{ statisticItem.value }}</div>
            <div class="consultant-card-statistic-label">{{ statisticItem.label }}</div>
          </div>
        </div>
        <a-button class="consultant-card-button" type="primary" @click="handleAssign(data)">指派接单</a-button>
      </div>
    </div>
    <div
      v-if="data.isPerson"
      :style="'right:' + (data.isRecommend ? '110px' : '4px')"
      class="consultant-card-recommand-person"
    >
      <img :src="PersonImg" />
      <span>意向顾问</span>
    </div>
    <div v-if="data.isRecommend" class="consultant-card-recommand-tag">
      <img :src="LikeImg" />
      <span>系统推荐</span>
    </div>
    <a-modal class="consultant-card-modal" width="90%" v-model:open="open" title="会议明细" centered>
      <a-table :dataSource="orderList" :columns="columns" :pagination="pagination" @change="handleChange">
        <template #bodyCell="{ column, record }" :page="searchParams.pageNum">
          <span v-if="column.dataIndex === 'startDate'">
            {{
              record.startDate.split(' ')[0].replaceAll('-', '/') +
              '-' +
              record.endDate.split(' ')[0].replaceAll('-', '/')
            }}
          </span>
          <span v-if="column.dataIndex === 'operatorName'">
            {{ record.operatorName }}{{ record.operatorName ? '/' : '' }}{{ record.operatorCode }}
          </span>
          <span v-if="column.dataIndex === 'processNode'">
            {{ ProcessNode.ofType(record.processNode).desc }}
          </span>
        </template>
      </a-table>
      <template #footer> </template>
    </a-modal>
  </div>
</template>
<style lang="less" scoped>
.consultant-card {
  position: relative;
  display: flex;
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 8px;
  margin-top: 24px;

  .consultant-card-left {
    display: flex;
    flex-direction: column;
    align-items: center;
    img {
      border-radius: 50%;
      width: 64px;
      height: 64px;
      object-fit: cover;
    }
    a {
      margin-top: 16px;
      color: #1868db;
      line-height: 20px;
      text-align: center;
      text-decoration-line: underline;
    }
  }
  .consultant-card-right {
    margin-left: 10px;
    flex: 1;
    .consultant-card-right-top {
      margin-top: 7px;
      .consultant-card-name {
        font-weight: 700;
        font-size: 16px;
        color: #1d2129;
        line-height: 22px;
      }
      // 溢出显示省略号
      .consultant-card-desc {
        // height: 20px;

        white-space: nowrap;
        width: 400px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        margin-bottom: -5px;
        color: #86909c;
        line-height: 20px;
        margin-left: 12px;
      }
    }
    .consultant-card-right-middle {
      display: flex;
      margin-top: 8px;
      .consultant-card-performance {
        display: flex;
        align-items: center;
        height: 20px;
        color: #1d2129;
        .consultant-card-performance-icon {
          width: 6px;
          height: 6px;
          border-radius: 6px;
          margin-right: 4px;
          margin-top: 2px;
        }
        .consultant-card-performance-divider {
          margin: -2px 10px 0;
        }
      }
    }
    .consultant-card-right-bottom {
      display: flex;
      margin-top: 23px;
      justify-content: space-between;
      align-items: center;
      .consultant-card-statistics {
        display: flex;
        background: #f7f8fa;
        border-radius: 4px;
        padding: 8px 0;
        .consultant-card-statistic {
          padding: 0 18px;
          .consultant-card-statistic-label {
            color: #86909c;
            line-height: 20px;
          }
          .consultant-card-statistic-value {
            font-family: OPPOSans, OPPOSans;
            font-weight: 700;
            font-size: 16px;
            color: #1d2129;
            line-height: 25px;
          }
        }
      }
      .consultant-card-button {
        border-radius: 4px;
      }
    }
  }
  .consultant-card-recommand-tag {
    position: absolute;
    top: 4px;
    right: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255, 106, 0, 0.1);
    border-radius: 4px;
    width: 102px;
    height: 32px;
    color: #ff6a00;
    font-weight: 700;
    img {
      width: 14px;
      height: 14px;
      margin-right: 8px;
    }
  }
  .consultant-card-recommand-person {
    position: absolute;
    top: 4px;
    right: 110px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255, 106, 0, 0.1);
    border-radius: 4px;
    width: 102px;
    height: 32px;
    color: #ff6a00;
    font-weight: 700;
    img {
      width: 14px;
      height: 14px;
      margin-right: 8px;
    }
  }
}
:deep(.ant-modal-footer) {
  display: none;
}
</style>
