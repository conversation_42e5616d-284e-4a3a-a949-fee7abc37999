<script setup lang="ts">
// 会议估算
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, defineProps, defineEmits, defineExpose } from 'vue';
import { resolveParam, formatNumberThousands } from '@haierbusiness-front/utils';
import { useRoute } from 'vue-router';

const route = useRoute();

const props = defineProps({
  cacheStr: {
    type: String,
    default: '',
  },
  demandParams: {
    type: Object,
    default: {},
  },
});

const emit = defineEmits(['demandBudgetFunc', 'viewCalc']);

const testTime = localStorage.getItem('testProcessSignForCiCi') === '1' ? 1000 : 17000;
const calcBtnShow = ref<boolean>(false);

// 价格测算
const calcFn = (params) => {
  // 会议估算
  let calcNum = 0;

  // 住宿
  params.stays &&
    params.stays.forEach((e) => {
      // 单价*房间数
      calcNum = calcNum + e.calcUnitPrice * e.roomNum;
    });
  // console.log('%c [ 住宿 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // 会场
  params.places &&
    params.places.forEach((e) => {
      calcNum = calcNum + e.calcUnitPlacePrice;

      if (e.calcUnitLedPrice) {
        // 单价*LED数量
        calcNum = calcNum + e.calcUnitLedPrice * e.ledNum;
      }
      if (e.calcUnitTeaPrice) {
        // 茶歇单价*会场人数
        calcNum = calcNum + e.calcUnitTeaPrice * e.personNum;
      }
    });
  // console.log('%c [ 会场 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // 用餐
  params.caterings &&
    params.caterings.forEach((e) => {
      // 单价*用餐人数
      calcNum = calcNum + e.calcUnitPrice * e.personNum;
    });
  // console.log('%c [ 用餐 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // 用车
  params.vehicles &&
    params.vehicles.forEach((e) => {
      // 价格*用车数量
      calcNum = calcNum + e.calcUnitPrice * e.vehicleNum;
    });
  // console.log('%c [ 用车 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // 服务人员
  params.attendants &&
    params.attendants.forEach((e) => {
      // 单价*人数
      calcNum = calcNum + e.calcUnitPrice * e.personNum;
    });
  // console.log('%c [ 服务 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // 拓展活动
  params.activities &&
    params.activities.forEach((e) => {
      // 单价*人数
      calcNum = calcNum + e.calcUnitPrice * e.personNum;
    });
  // console.log('%c [ 活动 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // 保险
  params.insurances &&
    params.insurances.forEach((e) => {
      // 单价*人数
      calcNum = calcNum + e.calcUnitPrice * e.personNum;
    });
  // console.log('%c [ 保险 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // 布展物料
  if (params.material && params.material.calcTotalPrice) {
    // 费用标准/总
    calcNum = calcNum + params.material.demandTotalPrice;
  }
  // console.log('%c [ 布展物料 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // TODO
  // 票务预定
  if (params.traffic && params.traffic.calcTotalPrice) {
    calcNum = calcNum + params.traffic.demandTotalPrice;
  }
  // console.log('%c [ 票务 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // 礼品需求
  params.presents &&
    params.presents.forEach((e) => {
      calcNum = calcNum + e.calcTotalPrice;
    });
  // console.log('%c [ 礼品 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // 其他需求
  params.others &&
    params.others.forEach((e) => {
      calcNum = calcNum + e.calcTotalPrice;
    });
  // console.log('%c [ 其他需求 - 价格测算 ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // console.log('%c [ 测算金额 ]-64', 'font-size:13px; background:pink; color:#bf2c9f;', calcNum);

  // 测算总金额
  formState.calcTotalPrice = calcNum;
  // formState.demandTotalPrice = calcNum;
  AICalcPrice.value = calcNum;
};

// 会议估算表单
const formState = reactive({
  calcTotalPrice: null, // 测算总金额 ,传入后和后端计算金额做校验
  demandTotalPrice: null, // 需求总金额
});

const AICalcPrice = ref<number>(0); // AI预算测算价格
const aiTipShow = ref<boolean>(false); // AI预算测算价格
const delayedShow = ref<boolean>(false); // AI预算测算价格

const height = ref('');

// AI会务测算预算 - 按钮
const calculationBtn = () => {
  setTimeout(() => {
    aiTipShow.value = false;
  }, 1000);

  delayedShow.value = false;
  // 效果
  height.value = window.innerHeight + 'px';

  // isCalcBtnClick - 点击会务预算按钮
  emit('demandBudgetFunc', { ...formState, isCalcBtnClick: true }, () => {
    loaderShow();
    setTimeout(() => {
      loaderHide();
    }, testTime);
  });
};

// 暂存
const tempSave = () => {
  emit(
    'demandBudgetFunc',
    {
      ...formState,
    },
    () => {},
  );
};

defineExpose({ calcFn, tempSave });

// AI会务测算预算 - 确认按钮
const calcConfirm = () => {
  return new Promise((resolve, reject) => {
    calcCancel();

    if (!AICalcPrice.value) {
      message.error('AI预算有误，请重新填写需求！');
      reject(false);
      return;
    }

    formState.demandTotalPrice = AICalcPrice.value;
    calcBtnShow.value = true;

    emit('demandBudgetFunc', { ...formState, isCalcBtnClick: false }, () => {});

    resolve(true);
  });
};

const calcCancel = () => {
  aiTipShow.value = false;
  delayedShow.value = false;
};

// 查看估算结果
const viewCalcBtn = () => {
  emit('viewCalc', true);
};

const priceFocus = () => {
  if (delayedShow.value) {
    return;
  }

  aiTipShow.value = true;
};
// 价格修改
const priceEmit = () => {
  emit(
    'demandBudgetFunc',
    {
      ...formState,
      isCalcBtnClick: false,
    },
    () => {},
  );
};

onMounted(async () => {
  const record = resolveParam(route.query.record);

  // 反显
  if (props.cacheStr) {
    const cacheObj = JSON.parse(props.cacheStr);

    formState.calcTotalPrice = cacheObj.calcTotalPrice;
    // formState.demandTotalPrice = cacheObj.demandTotalPrice;

    if (record.priceSource === 'manage' && record.demandTotalPrice) {
      // 后台管理页面
      formState.demandTotalPrice = record.demandTotalPrice;
      return;
    }

    if (record.previewCalc === 'previewCalc' || record.previewCalc === 'preview') {
      // 价格测算
      await calcFn(cacheObj);

      if (record.demandTotalPrice && record.demandTotalPrice >= 0) {
        await calcConfirm();
      }

      // 预算价格为修改后金额
      formState.demandTotalPrice = record.demandTotalPrice;

      // 价格明细预览页面返回 - 显示会务预算
      // emit('demandBudgetFunc', { ...formState, isCalcBtnClick: false, }, () => {});
    }
  }
});

// 预算效果
const loading = ref(false);
const loader = ref<any>();

const loaderShow = () => {
  setTimeout(() => {
    aiTipShow.value = false;
    delayedShow.value = true;
  }, 1000);

  loader.value = new window.SVGLoader(document.getElementById('loader'), {
    speedIn: 300,
    speedOut: 600,
    easingIn: mina.easeinout,
    easingOut: mina.bounce,
  });
  loader.value.show();
  setTimeout(() => {
    loading.value = true;
    start();
    if (interval.value) clearInterval(interval.value);
    interval.value = setInterval(() => {
      if (currentProgress.value < 100) {
        currentProgress.value += 1;
        updateProgressBar(currentProgress.value);
      } else {
        clearInterval(interval.value);
      }
    }, 50);
    startProgress();
  }, 300);
};

const loaderHide = () => {
  loader.value.hide();
  loading.value = false;
  resetProgress();
};

const progressBar = ref<HTMLElement | null>();
const progressPercentage = ref<HTMLElement | null>();
const progressStatus = ref<HTMLElement | null>();
const progressDots = ref<NodeListOf<HTMLElement> | null>();
const interval = ref();
const currentProgress = ref<number>(0);
const start = () => {
  progressBar.value = document.querySelector('.progress-bar') as HTMLElement;
  progressPercentage.value = document.querySelector('.progress-percentage') as HTMLElement;
  progressStatus.value = document.querySelector('.progress_title') as HTMLElement;
  progressStatus.value = document.querySelector('.progress-status') as HTMLElement;
  progressDots.value = document.querySelectorAll('.progress-dot') as NodeListOf<HTMLElement>;
};

const updateProgressBar = (progress: number) => {
  progressBar.value!.style.width = `${progress}%`;
  progressPercentage.value!.textContent = `${progress}%`;

  // 更新状态文本
  if (progress < 12.5) {
    progressStatus.value!.textContent = '初始化...';
  } else if (progress < 25) {
    progressStatus.value!.textContent = '正在准备向AI服务发送查询指令';
  } else if (progress < 37.5) {
    progressStatus.value!.textContent = '系统正在初始化通信通道';
  } else if (progress < 50) {
    progressStatus.value!.textContent = '正在建立安全连接，保护数据隐私';
  } else if (progress < 62.5) {
    progressStatus.value!.textContent = 'AI正在理解您的会展需求';
  } else if (progress < 75) {
    progressStatus.value!.textContent = '正在对接行业数据库与历史案例库';
  } else if (progress < 87.5) {
    progressStatus.value!.textContent = '调用大数据模型进行价格预测与推荐';
  } else if (progress < 100) {
    progressStatus.value!.textContent = '正在综合多维度因素优化推荐方案';
  } else {
    progressStatus.value!.textContent = '数据已就绪！';
  }

  // 更新点的状态
  progressDots.value!.forEach((dot, index) => {
    const dotThreshold = index * 12.5;
    if (progress >= dotThreshold) {
      dot.classList.add('active');
    } else {
      dot.classList.remove('active');
    }
  });

  // 显示状态文本
  progressStatus.value!.classList.add('active');
};

const startProgress = () => {
  if (interval.value) clearInterval(interval.value);

  interval.value = setInterval(() => {
    if (currentProgress.value < 100) {
      currentProgress.value += 1;
      updateProgressBar(currentProgress.value);
    } else {
      clearInterval(interval.value);
    }
  }, 160);
};

const resetProgress = () => {
  if (interval.value) clearInterval(interval.value);
  currentProgress.value = 0;
  updateProgressBar(currentProgress.value);
};
</script>

<template>
  <!-- 会议估算 -->
  <div class="meeting_budget demand_pad24">
    <div class="budget_title">
      <span class="mr7">💰</span>
      <span>会议估算</span>
    </div>

    <div class="budget_contet mt12">
      <div class="budget_amount">
        <div class="amount_label">预计投入金额</div>
        <a-popconfirm :title="aiTipShow ? '温馨提示' : 'AI预计投入金额'" placement="topLeft" :showCancel="false">
          <template #okButton> </template>

          <template #description>
            <div v-show="aiTipShow" class="pr16">
              <div>您可以点击“AI会务测算预算”按钮，</div>
              <div>通过AI进行会议预计投入金额估算</div>
              <div class="mt10 demand_budget_tip">
                <div class="demand_budget_btn" @click="calculationBtn">
                  <div class="calculation_btn_img mr10"></div>
                  <span>AI会务测算预算</span>
                </div>
              </div>
            </div>

            <div v-show="delayedShow" class="pr16" style="width: 420px">
              <a-alert type="info">
                <template #description>
                  <div class="budget_ai_tip">
                    <div>当前金额为测算金额，可修改；</div>
                    <div class="mt5">
                      所有测算结果均通过AI大数据云计算算法生成，包含历史数据分析和实时接口调用，实际发生费用可能受市场波动、政策调整等因素影响，请以最终中标价格为准。
                    </div>
                  </div>

                  <div class="mt16">
                    测算价格为
                    <b style="color: #1868db; font-size: 16px">{{
                      AICalcPrice && formatNumberThousands(AICalcPrice)
                    }}</b>
                    元（仅供参考）,
                  </div>
                  <div class="demand_a_alert">
                    <span class="mr20">是否接收AI预算结果</span>
                    <div>
                      <a-button size="small" class="mr8" @click="calcCancel">取消</a-button>
                      <a-button size="small" type="primary" @click="calcConfirm">确定</a-button>
                    </div>
                  </div>
                </template>
              </a-alert>
            </div>
          </template>

          <div class="amount_num">
            <a-input-number
              v-model:value="formState.demandTotalPrice"
              @blur="priceEmit"
              @focus="priceFocus"
              placeholder=""
              allow-clear
              :min="1"
              :precision="2"
              class="amount_inp"
              :controls="false"
              style="width: 230px"
            />
            <!-- :disabled="!calcBtnShow" -->
            <span class="amount_unit ml6">元</span>
          </div>
        </a-popconfirm>
      </div>
      <!--
      <a-popconfirm
        :open="delayedShow"
        title="AI预计投入金额"
        placement="topLeft"
        ok-text="确定"
        cancel-text="取消"
        @confirm="calcConfirm"
        @cancel="delayedShow = false"
      >
        <template #description>
          <div class="pr16">
            <div>
              测算价格为
              <b>{{ AICalcPrice && formatNumberThousands(AICalcPrice) }}</b>
              元（仅供参考）,
            </div>
            <div>是否接收AI预算结果</div>
          </div>
        </template>

        <div class="budget_btn" @click="calculationBtn">
          <div class="calculation_btn_img mr10"></div>
          <span>AI会务测算预算</span>
        </div>
      </a-popconfirm> -->

      <a-button v-if="calcBtnShow" class="ml24" @click="viewCalcBtn()">查看估算结果</a-button>

      <!-- <div v-show="formState.demandTotalPrice" class="budget_tip ml15">当前金额为测算金额，可修改</div> -->
    </div>

    <!-- <div v-show="formState.demandTotalPrice" class="budget_ai_tip">
      <div class="mt16">当前金额为测算金额，可修改；</div>
      <div class="mt5">
        所有测算结果均通过AI大数据云计算算法生成，包含历史数据分析和实时接口调用，实际发生费用可能受市场波动、政策调整等因素影响，请以最终中标价格为准。
      </div>
    </div> -->

    <!-- 预算效果 -->
    <div
      id="loader"
      :style="{ height: height }"
      class="pageload-overlay"
      data-opening="M 0,0 0,60 80,60 80,0 z M 80,0 40,30 0,60 40,30 z"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 80 60" preserveAspectRatio="none">
        <path d="M 0,0 0,60 80,60 80,0 Z M 80,0 80,60 0,60 0,0 Z" />
      </svg>

      <div class="loading" v-show="loading">
        <div class="spinner">
          <div class="progressCon">
            <div class="progress-container">
              <div class="progress_title">
                <span>AI大模型会务费用智能分析引擎</span>
                <span class="progress_title_tip"> -- （DeepSeek深度接入版）</span>
              </div>
              <div class="progress-status">处理中...</div>
              <div class="progress-bar-wrapper">
                <div class="progress-bar"></div>
              </div>
              <div class="progress-info">
                <div class="progress-label">数据加载中</div>
                <div class="progress-percentage">0%</div>
              </div>
              <div class="progress-dots">
                <div class="progress-dot"></div>
                <div class="progress-dot"></div>
                <div class="progress-dot"></div>
                <div class="progress-dot"></div>
                <div class="progress-dot"></div>
                <div class="progress-dot"></div>
                <div class="progress-dot"></div>
                <div class="progress-dot"></div>
                <div class="progress-dot"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.spinner {
  display: flex;
  width: 800px;
  /* height: 300px; */
  flex-direction: column;
  align-items: center;

  .face {
    display: flex;
    width: 60px;
  }
}

.meeting_budget {
  background: linear-gradient(180deg, #d9e8ff 0%, #edf4ff 33%, #ffffff 100%);
  border-radius: 12px;
  border: 3px solid #ffffff;

  .budget_title {
    font-weight: 500;
    font-size: 20px;
    color: #1d2129;
  }

  .budget_contet {
    display: flex;
    align-items: center;

    .budget_amount {
      padding: 0 28px 0 24px;
      width: 400px;
      height: 56px;
      text-align: center;
      line-height: 56px;
      background: #d7e7ff;
      border-radius: 4px;

      display: flex;
      justify-content: space-between;

      .amount_label {
        color: #262626;
      }
      .amount_num {
        display: flex;
        align-items: center;

        .amount_inp {
        }
        :deep(.ant-input-number .ant-input-number-input) {
          text-align: right;
        }
        .amount_unit {
          font-weight: 500;
          color: #4e5969;
        }
      }
    }

    .budget_tip {
      color: #86909c;
      line-height: 22px;
    }
  }
}
</style>

<style scoped lang="less">
.title {
  display: flex;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  width: 100%;
  padding-bottom: 10px;
}

.loading {
  position: absolute;
  z-index: 99999;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #0f1923;
}

.container {
  display: none;
  min-height: 100%;
}

.container .show {
  display: block;
}

.pageload-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: none;
  z-index: 999999;
}

.pageload-overlay.show {
  display: block;
}

.pageload-overlay svg {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.pageload-overlay svg path {
  fill: #fff;
}
</style>

<style scoped lang="less">
.progressCon {
  width: 100%;
}

.progress-container {
  width: 100%;
  position: relative;
}

.progress-bar-wrapper {
  width: 100%;
  position: relative;
  height: 8px;
  background: rgba(32, 152, 209, 0.2);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(32, 152, 209, 0.3);
}

.progress-bar {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, #2098d1, #43e7ff);
  position: relative;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  color: #2098d1;
  font-size: 14px;
}

.progress-percentage {
  font-weight: bold;
  font-size: 18px;
  color: #43e7ff;
}

.progress_title {
  position: absolute;
  top: -140px;
  left: 0;
  width: 100%;
  text-align: center;
  color: #43e7ff;
  font-size: 40px;

  animation: blink-and-change-color 5s infinite ease-in-out;
  transition: color 0.5s linear, opacity 0.5s ease;

  .progress_title_tip {
    font-size: 14px;
  }
}
.progress-status {
  position: absolute;
  top: -25px;
  left: 0;
  width: 100%;
  text-align: center;
  color: #43e7ff;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progress-status.active {
  opacity: 1;
}

.progress-dots {
  position: absolute;
  bottom: -20px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.progress-dot {
  width: 6px;
  height: 6px;
  background-color: rgba(32, 152, 209, 0.3);
  border-radius: 50%;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.progress-dot.active {
  background-color: #43e7ff;
  transform: scale(1.5);
  box-shadow: 0 0 8px #43e7ff;
}

.controls {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.btn {
  padding: 10px 20px;
  border: none;
  background: rgba(32, 152, 209, 0.2);
  color: #43e7ff;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  border: 1px solid rgba(67, 231, 255, 0.3);
}

.btn:hover {
  background: rgba(32, 152, 209, 0.4);
  box-shadow: 0 0 10px rgba(67, 231, 255, 0.5);
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 定义名为blink-and-change-color的动画关键帧 */
@keyframes blink-and-change-color {
  /* 动画开始时的状态，文字完全显示，颜色为红色 */
  0% {
    opacity: 0.7;
    color: #43e7ff;
  }
  /* 动画进行到25%时的状态，文字完全显示，颜色为橙色 */
  25% {
    opacity: 0.9;
    color: #43e7ff;
  }
  /* 动画进行到50%时的状态，文字完全隐藏，颜色为黄色 */
  50% {
    opacity: 0.7;
    color: #43e7ff;
  }
  /* 动画进行到75%时的状态，文字完全显示，颜色为绿色 */
  75% {
    opacity: 0.9;
    color: #43e7ff;
  }
  /* 动画结束时的状态，文字完全显示，颜色为蓝色 */
  100% {
    opacity: 0.7;
    color: #43e7ff;
  }
}
</style>
<style>
.demand_budget_tip {
  display: flex;
  justify-content: center;
}

.demand_budget_btn {
  width: 138px;
  height: 36px;
  background: linear-gradient(180deg, #35a1ef 0%, #1868db 100%);
  box-shadow: 0px 2px 8px 0px rgba(0, 103, 216, 0.1);
  border-radius: 4px;

  font-weight: 500;
  color: #ffffff;
  text-shadow: 0px 2px 8px rgba(0, 103, 216, 0.1);
  cursor: pointer;
  user-select: none;

  display: flex;
  justify-content: center;
  align-items: center;

  .calculation_btn_img {
    width: 14px;
    height: 20px;
    background: url('@/assets/image/demand/demand_calculation.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

.budget_ai_tip {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.8);
}

.demand_a_alert {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
